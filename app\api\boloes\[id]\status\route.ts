import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery, executeQuerySingle } from "@/lib/database-config"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`🔍 Buscando status do bolão ID: ${params.id}`)

    const bolaoId = parseInt(params.id)

    try {
      await initializeDatabase()

      // Buscar informações do bolão com timeout
      const bolao = await Promise.race([
        executeQuery(`SELECT * FROM boloes WHERE id = ?`, [bolaoId]),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout na consulta do bolão')), 3000)
        )
      ])

      if (!bolao || bolao.length === 0) {
        return NextResponse.json(
          { success: false, error: "Bolão não encontrado" },
          { status: 404 }
        )
      }

      const bolaoData = bolao[0]

    // Buscar partidas do bolão
    let partidas = []
    try {
      const partidasSelecionadas = JSON.parse(bolaoData.partidas_selecionadas || '[]')
      const partidasIds = partidasSelecionadas.map((p: any) => p.id || p)
      
      if (partidasIds.length > 0) {
        const placeholders = partidasIds.map(() => '?').join(',')
        partidas = await executeQuery(`
          SELECT 
            j.*,
            tc.nome as time_casa_nome,
            tf.nome as time_fora_nome
          FROM jogos j
          LEFT JOIN times tc ON j.time_casa_id = tc.id
          LEFT JOIN times tf ON j.time_fora_id = tf.id
          WHERE j.id IN (${placeholders})
          ORDER BY j.data_jogo ASC
        `, partidasIds)
      }
    } catch (error) {
      console.error('Erro ao buscar partidas:', error)
    }

    // Verificar se algum jogo já começou
    const agora = new Date()
    const jogosIniciados = partidas.filter((partida: any) => {
      const dataJogo = new Date(partida.data_jogo)
      return dataJogo <= agora
    })

    // Verificar se todos os jogos terminaram
    const jogosFinalizados = partidas.filter((partida: any) => {
      return partida.status === 'finalizado'
    })

    const apostasEncerradas = jogosIniciados.length > 0
    const bolaoFinalizado = jogosFinalizados.length === partidas.length && partidas.length > 0

    // Se o bolão finalizou, buscar ranking e calcular prêmios
    let ranking = []
    if (bolaoFinalizado) {
      ranking = await executeQuery(`
        SELECT
          u.id,
          u.nome,
          COUNT(CASE WHEN ad.acertou = 1 THEN 1 END) as acertos,
          COUNT(ad.id) as total_apostas,
          ROUND((COUNT(CASE WHEN ad.acertou = 1 THEN 1 END) / COUNT(ad.id)) * 100, 2) as percentual_acertos,
          a.id as aposta_id
        FROM usuarios u
        JOIN apostas a ON u.id = a.usuario_id
        JOIN aposta_detalhes ad ON a.id = ad.aposta_id
        JOIN jogos j ON ad.jogo_id = j.id
        WHERE a.bolao_id = ? AND a.status = 'paga'
        GROUP BY u.id, u.nome, a.id
        ORDER BY acertos DESC, percentual_acertos DESC, a.data_aposta ASC
      `, [bolaoId])

      // Calcular e distribuir prêmios se ainda não foram calculados
      if (ranking.length > 0) {
        await calcularPremiosBolao(bolaoId, ranking)
      }
    }

      console.log(`✅ Status do bolão ${bolaoId} carregado do banco`)

      return NextResponse.json({
        success: true,
        bolao: {
          id: bolaoData.id,
          nome: bolaoData.nome,
          status: bolaoData.status
        },
        apostas_encerradas: apostasEncerradas,
        bolao_finalizado: bolaoFinalizado,
        total_partidas: partidas.length,
        jogos_iniciados: jogosIniciados.length,
        jogos_finalizados: jogosFinalizados.length,
        ranking: ranking,
        proxima_partida: jogosIniciados.length === 0 && partidas.length > 0 ? partidas[0] : null,
        source: 'database'
      })

    } catch (dbError) {
      console.warn(`⚠️ Erro ao acessar banco, retornando dados básicos:`, (dbError as Error).message)

      // Retornar dados básicos sem erro
      return NextResponse.json({
        success: true,
        bolao: {
          id: bolaoId,
          nome: `Bolão ${bolaoId}`,
          status: 'ativo'
        },
        apostas_encerradas: false,
        bolao_finalizado: false,
        total_partidas: 0,
        jogos_iniciados: 0,
        jogos_finalizados: 0,
        ranking: [],
        proxima_partida: null,
        source: 'fallback'
      })
    }

  } catch (error) {
    console.error("❌ Erro ao verificar status do bolão:", error)
    return NextResponse.json(
      { success: false, error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

// Função para calcular e distribuir prêmios do bolão
async function calcularPremiosBolao(bolaoId: number, ranking: any[]) {
  try {
    console.log(`🏆 Calculando prêmios para bolão ${bolaoId}`)

    // Buscar dados do bolão
    const bolao = await executeQuerySingle(`
      SELECT premiacao_11_pontos, premiacao_maior_pontuador
      FROM boloes
      WHERE id = ?
    `, [bolaoId])

    if (!bolao) {
      console.error(`❌ Bolão ${bolaoId} não encontrado`)
      return
    }

    const premiacao11Pontos = parseFloat(bolao.premiacao_11_pontos) || 500.00
    const premiacaoMaiorPontuador = parseFloat(bolao.premiacao_maior_pontuador) || 10.00

    // Verificar se já existem prêmios calculados para este bolão
    const premiosExistentes = await executeQuery(`
      SELECT COUNT(*) as total FROM premios WHERE bolao_id = ?
    `, [bolaoId])

    if (premiosExistentes[0]?.total > 0) {
      console.log(`ℹ️ Prêmios já calculados para bolão ${bolaoId}`)
      return
    }

    // Encontrar quem fez 11 pontos
    const vencedores11Pontos = ranking.filter((r: any) => r.acertos === 11)

    // Se tem vencedor de 11 pontos, não há maior pontuador
    if (vencedores11Pontos.length > 0) {
      console.log(`🎯 ${vencedores11Pontos.length} vencedor(es) com 11 pontos encontrado(s)`)

      // Dividir prêmio de 11 pontos entre os vencedores
      const valorPorVencedor = premiacao11Pontos / vencedores11Pontos.length

      for (const vencedor of vencedores11Pontos) {
        await executeQuery(`
          INSERT INTO premios (bolao_id, usuario_id, aposta_id, acertos, valor_premio, status, data_premio)
          VALUES (?, ?, ?, ?, ?, 'pendente', NOW())
        `, [bolaoId, vencedor.id, vencedor.aposta_id, vencedor.acertos, valorPorVencedor])

        console.log(`💰 Prêmio de R$ ${valorPorVencedor.toFixed(2)} para ${vencedor.nome} (11 pontos)`)
      }
    } else {
      // Não há vencedor de 11 pontos, buscar maior pontuador
      const maiorPontuacao = Math.max(...ranking.map((r: any) => r.acertos))

      if (maiorPontuacao > 0) {
        const maioresPontuadores = ranking.filter((r: any) => r.acertos === maiorPontuacao)

        console.log(`🥇 ${maioresPontuadores.length} maior(es) pontuador(es) com ${maiorPontuacao} pontos`)

        // Dividir prêmio de maior pontuador entre os empatados
        const valorPorMaiorPontuador = premiacaoMaiorPontuador / maioresPontuadores.length

        for (const maiorPontuador of maioresPontuadores) {
          await executeQuery(`
            INSERT INTO premios (bolao_id, usuario_id, aposta_id, acertos, valor_premio, status, data_premio)
            VALUES (?, ?, ?, ?, ?, 'pendente', NOW())
          `, [bolaoId, maiorPontuador.id, maiorPontuador.aposta_id, maiorPontuador.acertos, valorPorMaiorPontuador])

          console.log(`🏅 Prêmio de R$ ${valorPorMaiorPontuador.toFixed(2)} para ${maiorPontuador.nome} (${maiorPontuacao} pontos - maior pontuador)`)
        }
      }
    }

    console.log(`✅ Prêmios calculados e distribuídos para bolão ${bolaoId}`)

  } catch (error) {
    console.error(`❌ Erro ao calcular prêmios do bolão ${bolaoId}:`, error)
  }
}
