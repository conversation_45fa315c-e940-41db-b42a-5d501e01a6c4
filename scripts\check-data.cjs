const mysql = require('mysql2/promise')

async function checkData() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '',
    database: 'sistema-bolao-top'
  })

  try {
    console.log('✅ Conectado ao banco de dados')

    // Verificar jogos
    const [jogos] = await connection.execute('SELECT COUNT(*) as total FROM jogos')
    console.log(`📊 Total de jogos: ${jogos[0].total}`)

    // Verificar bolao_jogos
    const [bolaoJogos] = await connection.execute('SELECT COUNT(*) as total FROM bolao_jogos')
    console.log(`📊 Total de bolao_jogos: ${bolaoJogos[0].total}`)

    // Verificar bolões
    const [boloes] = await connection.execute('SELECT id, nome, status FROM boloes LIMIT 5')
    console.log('📊 Bolões:')
    boloes.forEach(bolao => {
      console.log(`   - ID: ${bolao.id}, Nome: ${bolao.nome}, Status: ${bolao.status}`)
    })

    // Verificar se existe o bolão 105
    const [bolao105] = await connection.execute('SELECT * FROM boloes WHERE id = 105')
    if (bolao105.length > 0) {
      console.log('📊 Bolão 105 encontrado:', bolao105[0])
      
      // Verificar jogos do bolão 105
      const [jogosBolao] = await connection.execute(`
        SELECT COUNT(*) as total 
        FROM bolao_jogos bj 
        WHERE bj.bolao_id = 105
      `)
      console.log(`📊 Jogos associados ao bolão 105: ${jogosBolao[0].total}`)

      // Verificar partidas_selecionadas do bolão 105
      if (bolao105[0].partidas_selecionadas) {
        try {
          const partidas = JSON.parse(bolao105[0].partidas_selecionadas)
          console.log(`📊 Partidas selecionadas no bolão 105: ${partidas.length}`)
          console.log('📊 Primeiras 3 partidas:', partidas.slice(0, 3))
        } catch (e) {
          console.log('❌ Erro ao parsear partidas_selecionadas:', e.message)
        }
      }
    } else {
      console.log('❌ Bolão 105 não encontrado')
    }

    // Verificar times
    const [times] = await connection.execute('SELECT COUNT(*) as total FROM times')
    console.log(`📊 Total de times: ${times[0].total}`)

    // Verificar campeonatos
    const [campeonatos] = await connection.execute('SELECT COUNT(*) as total FROM campeonatos')
    console.log(`📊 Total de campeonatos: ${campeonatos[0].total}`)

  } catch (error) {
    console.error('❌ Erro:', error)
  } finally {
    await connection.end()
  }
}

checkData()
