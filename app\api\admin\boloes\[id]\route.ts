import { type NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery, executeQuerySingle } from "@/lib/database-config"

export async function PATCH(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    console.log(`🔄 Tentando atualizar bolão ID: ${params.id}`)

    const bolaoId = parseInt(params.id)
    const data = await request.json()

    // Tentar atualizar no banco com timeout
    try {
      await initializeDatabase()

      // Verificar se o bolão existe com timeout
      const bolao = await Promise.race([
        executeQuerySingle('SELECT id FROM boloes WHERE id = ?', [bolaoId]),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout na verificação do bolão')), 3000)
        )
      ])

      if (!bolao) {
        return NextResponse.json(
          {
            success: false,
            error: "Bolão não encontrado",
          },
          { status: 404 }
        )
      }

      // Atualizar status do bolão com timeout
      if (data.status) {
        await Promise.race([
          executeQuery('UPDATE boloes SET status = ? WHERE id = ?', [data.status, bolaoId]),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Timeout na atualização')), 3000)
          )
        ])

        console.log(`✅ Bolão ${bolaoId} atualizado no banco`)
        return NextResponse.json({
          success: true,
          message: `Bolão ${data.status === 'ativo' ? 'ativado' : 'desativado'} com sucesso`,
          source: 'database'
        })
      }

      return NextResponse.json(
        {
          success: false,
          error: "Dados inválidos para atualização",
        },
        { status: 400 }
      )

    } catch (dbError) {
      console.error(`❌ Erro ao atualizar bolão no banco:`, (dbError as Error).message)

      return NextResponse.json({
        success: false,
        error: 'Erro ao atualizar bolão no banco de dados',
        message: (dbError as Error).message
      }, { status: 500 })
    }
  } catch (error) {
    console.error("Erro ao atualizar bolão:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido",
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    console.log(`🗑️ Tentando deletar bolão ID: ${params.id}`)

    const bolaoId = parseInt(params.id)

    // Tentar deletar do banco com timeout
    try {
      await initializeDatabase()

      // Verificar se o bolão existe com timeout
      const bolao = await Promise.race([
        executeQuerySingle('SELECT id, nome FROM boloes WHERE id = ?', [bolaoId]),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout na verificação do bolão')), 3000)
        )
      ])

      if (!bolao) {
        return NextResponse.json(
          {
            success: false,
            error: "Bolão não encontrado",
          },
          { status: 404 }
        )
      }

      // Verificar se há apostas associadas com timeout
      const apostas = await Promise.race([
        executeQuerySingle('SELECT COUNT(*) as total FROM apostas WHERE bolao_id = ?', [bolaoId]),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout na verificação de apostas')), 3000)
        )
      ])

      if (apostas && apostas.total > 0) {
        return NextResponse.json(
          {
            success: false,
            error: "Não é possível deletar um bolão que já possui apostas",
          },
          { status: 400 }
        )
      }

      // Deletar o bolão com timeout
      await Promise.race([
        executeQuery('DELETE FROM boloes WHERE id = ?', [bolaoId]),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout na deleção')), 3000)
        )
      ])

      console.log(`✅ Bolão ${bolaoId} deletado do banco`)
      return NextResponse.json({
        success: true,
        message: "Bolão deletado com sucesso",
        source: 'database'
      })

    } catch (dbError) {
      console.error(`❌ Erro ao deletar bolão do banco:`, (dbError as Error).message)

      return NextResponse.json({
        success: false,
        error: 'Erro ao deletar bolão do banco de dados',
        message: (dbError as Error).message
      }, { status: 500 })
    }
  } catch (error) {
    console.error("Erro ao deletar bolão:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido",
      },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    console.log(`📝 Tentando editar bolão ID: ${params.id}`)

    const bolaoId = parseInt(params.id)
    const data = await request.json()

    console.log(`📦 Dados recebidos:`, {
      nome: data.nome,
      valor_aposta: data.valor_aposta,
      premio_total: data.premio_total,
      premiacao_11_pontos: data.premiacao_11_pontos,
      premiacao_maior_pontuador: data.premiacao_maior_pontuador
    })

    // Atualizar bolão no banco de dados
    await executeQuery(`
      UPDATE boloes SET
        nome = ?,
        descricao = ?,
        valor_aposta = ?,
        premio_total = ?,
        premiacao_11_pontos = ?,
        premiacao_maior_pontuador = ?,
        max_participantes = ?,
        data_inicio = ?,
        data_fim = ?,
        banner_image = ?
      WHERE id = ?
    `, [
      data.nome || 'Bolão Brasil',
      data.descricao || 'Bolão de apostas esportivas',
      parseFloat(data.valor_aposta) || 25.00,
      parseFloat(data.premio_total) || 1000.00,
      parseFloat(data.premiacao_11_pontos) || 500.00,
      parseFloat(data.premiacao_maior_pontuador) || 10.00,
      data.max_participantes || 100,
      data.data_inicio,
      data.data_fim,
      data.banner_image || null,
      bolaoId
    ])

    console.log(`✅ Bolão ${bolaoId} editado com sucesso`)

    return NextResponse.json({
      success: true,
      message: "Bolão atualizado com sucesso",
      source: 'database'
    })

  } catch (error) {
    console.error("Erro ao atualizar bolão:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido",
      },
      { status: 500 }
    )
  }
}
