-- =====================================================
-- SCRIPT PARA INSERIR BOLÃO QUE APARECE NO FRONTEND
-- =====================================================
-- Este script cria um bolão completo com jogos que aparecerá na página inicial

USE `sistema-bolao-top`;

-- =====================================================
-- 1. INSERIR BOLÃO PRINCIPAL
-- =====================================================
INSERT INTO `boloes` (
  `id`,
  `nome`, 
  `descricao`, 
  `valor_aposta`, 
  `premio_total`, 
  `data_inicio`, 
  `data_fim`, 
  `status`, 
  `criado_por`, 
  `campeonatos_selecionados`,
  `data_criacao`
) VALUES (
  200,
  'Bolão da Copa 2025', 
  'Participe do maior bolão da temporada! Apostas em jogos selecionados dos principais campeonatos.', 
  25.00, 
  5000.00, 
  '2025-07-29 10:00:00', 
  '2025-08-15 23:59:59', 
  'ativo', 
  1,
  '["BSA", "PL", "LL"]',
  NOW()
);

-- =====================================================
-- 2. INSERIR JOGOS PARA O BOLÃO
-- =====================================================

-- Inserir alguns jogos futuros se não existirem
INSERT IGNORE INTO `jogos` (
  `id`,
  `campeonato_id`,
  `time_casa_id`,
  `time_fora_id`,
  `data_jogo`,
  `status`,
  `rodada`
) VALUES 
-- Brasileirão
(600001, 1, 1, 2, '2025-08-01 20:00:00', 'agendado', 20),  -- Flamengo vs Palmeiras
(600002, 1, 3, 4, '2025-08-01 21:30:00', 'agendado', 20),  -- São Paulo vs Corinthians
(600003, 1, 5, 6, '2025-08-02 16:00:00', 'agendado', 20),  -- Santos vs Grêmio
(600004, 1, 7, 8, '2025-08-02 18:30:00', 'agendado', 20),  -- Internacional vs Atlético-MG
(600005, 1, 9, 10, '2025-08-02 20:00:00', 'agendado', 20), -- Cruzeiro vs Botafogo

-- Premier League (usando IDs de times existentes)
(600006, 4, 1, 3, '2025-08-03 11:30:00', 'agendado', 1),   -- Time 1 vs Time 3
(600007, 4, 2, 4, '2025-08-03 14:00:00', 'agendado', 1),   -- Time 2 vs Time 4
(600008, 4, 5, 7, '2025-08-03 16:30:00', 'agendado', 1),   -- Time 5 vs Time 7

-- La Liga (usando IDs de times existentes)
(600009, 5, 6, 8, '2025-08-04 16:00:00', 'agendado', 1),   -- Time 6 vs Time 8
(600010, 5, 9, 1, '2025-08-04 18:30:00', 'agendado', 1),   -- Time 9 vs Time 1
(600011, 5, 10, 2, '2025-08-04 21:00:00', 'agendado', 1);  -- Time 10 vs Time 2

-- =====================================================
-- 3. ASSOCIAR JOGOS AO BOLÃO
-- =====================================================
INSERT IGNORE INTO `bolao_jogos` (`bolao_id`, `jogo_id`) VALUES
(200, 600001),
(200, 600002),
(200, 600003),
(200, 600004),
(200, 600005),
(200, 600006),
(200, 600007),
(200, 600008),
(200, 600009),
(200, 600010),
(200, 600011);

-- =====================================================
-- 4. INSERIR MAIS UM BOLÃO SECUNDÁRIO
-- =====================================================
INSERT INTO `boloes` (
  `id`,
  `nome`, 
  `descricao`, 
  `valor_aposta`, 
  `premio_total`, 
  `data_inicio`, 
  `data_fim`, 
  `status`, 
  `criado_por`, 
  `campeonatos_selecionados`,
  `data_criacao`
) VALUES (
  201,
  'Bolão Brasileirão 2025', 
  'Apostas exclusivas do Campeonato Brasileiro. Participe e concorra a prêmios incríveis!', 
  15.00, 
  2500.00, 
  '2025-07-29 08:00:00', 
  '2025-08-10 23:59:59', 
  'ativo', 
  1,
  '["BSA"]',
  NOW()
);

-- Associar apenas jogos do Brasileirão ao segundo bolão
INSERT IGNORE INTO `bolao_jogos` (`bolao_id`, `jogo_id`) VALUES
(201, 600001),
(201, 600002),
(201, 600003),
(201, 600004),
(201, 600005);

-- =====================================================
-- 5. INSERIR BOLÃO EM BREVE
-- =====================================================
INSERT INTO `boloes` (
  `id`,
  `nome`, 
  `descricao`, 
  `valor_aposta`, 
  `premio_total`, 
  `data_inicio`, 
  `data_fim`, 
  `status`, 
  `criado_por`, 
  `campeonatos_selecionados`,
  `data_criacao`
) VALUES (
  202,
  'Super Bolão Agosto', 
  'Em breve! O maior bolão do mês com jogos imperdíveis dos principais campeonatos europeus.', 
  50.00, 
  10000.00, 
  '2025-08-05 10:00:00', 
  '2025-08-20 23:59:59', 
  'em_breve', 
  1,
  '["PL", "LL", "BSA"]',
  NOW()
);

-- =====================================================
-- 6. VERIFICAR RESULTADOS
-- =====================================================
SELECT 'Bolões inseridos com sucesso!' as resultado;

-- Mostrar bolões criados
SELECT 
  id,
  nome,
  valor_aposta,
  premio_total,
  status,
  (SELECT COUNT(*) FROM bolao_jogos WHERE bolao_id = boloes.id) as total_jogos
FROM boloes 
WHERE id IN (200, 201, 202)
ORDER BY id;

-- Mostrar jogos do bolão principal
SELECT 
  bj.bolao_id,
  j.id as jogo_id,
  tc.nome as time_casa,
  tf.nome as time_fora,
  c.nome as campeonato,
  j.data_jogo,
  j.status
FROM bolao_jogos bj
JOIN jogos j ON bj.jogo_id = j.id
LEFT JOIN times tc ON j.time_casa_id = tc.id
LEFT JOIN times tf ON j.time_fora_id = tf.id
LEFT JOIN campeonatos c ON j.campeonato_id = c.id
WHERE bj.bolao_id = 200
ORDER BY j.data_jogo;
