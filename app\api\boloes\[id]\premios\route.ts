import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await initializeDatabase()
    
    const bolaoId = parseInt(params.id)
    
    console.log(`🏆 Buscando prêmios do bolão ${bolaoId}`)

    // Buscar prêmios do bolão
    const premios = await executeQuery(`
      SELECT 
        p.id,
        p.acertos,
        p.valor_premio,
        p.status,
        p.data_premio,
        p.data_pagamento,
        u.nome as usuario_nome,
        u.email as usuario_email,
        CASE 
          WHEN p.acertos = 11 THEN '11 pontos'
          ELSE 'maior pontuador'
        END as tipo_premio
      FROM premios p
      JOIN usuarios u ON p.usuario_id = u.id
      WHERE p.bolao_id = ?
      ORDER BY p.valor_premio DESC, p.acertos DESC, p.data_premio ASC
    `, [bolaoId])

    // Buscar informações do bolão
    const bolao = await executeQuery(`
      SELECT 
        nome,
        status,
        premiacao_11_pontos,
        premiacao_maior_pontuador
      FROM boloes 
      WHERE id = ?
    `, [bolaoId])

    if (bolao.length === 0) {
      return NextResponse.json(
        { success: false, error: "Bolão não encontrado" },
        { status: 404 }
      )
    }

    // Calcular estatísticas dos prêmios
    const totalPremios = premios.length
    const valorTotalDistribuido = premios.reduce((sum: number, p: any) => sum + parseFloat(p.valor_premio), 0)
    const premiosPagos = premios.filter((p: any) => p.status === 'pago').length
    const premiosPendentes = premios.filter((p: any) => p.status === 'pendente').length

    // Agrupar prêmios por tipo
    const premiosPor11Pontos = premios.filter((p: any) => p.acertos === 11)
    const premiosMaiorPontuador = premios.filter((p: any) => p.acertos < 11)

    console.log(`✅ ${totalPremios} prêmios encontrados para bolão ${bolaoId}`)

    return NextResponse.json({
      success: true,
      bolao: {
        id: bolaoId,
        nome: bolao[0].nome,
        status: bolao[0].status,
        premiacao_11_pontos: parseFloat(bolao[0].premiacao_11_pontos),
        premiacao_maior_pontuador: parseFloat(bolao[0].premiacao_maior_pontuador)
      },
      premios: premios.map((p: any) => ({
        id: p.id,
        usuario_nome: p.usuario_nome,
        usuario_email: p.usuario_email,
        acertos: p.acertos,
        valor_premio: parseFloat(p.valor_premio),
        tipo_premio: p.tipo_premio,
        status: p.status,
        data_premio: p.data_premio,
        data_pagamento: p.data_pagamento
      })),
      estatisticas: {
        total_premios: totalPremios,
        valor_total_distribuido: valorTotalDistribuido,
        premios_pagos: premiosPagos,
        premios_pendentes: premiosPendentes,
        premios_11_pontos: {
          quantidade: premiosPor11Pontos.length,
          valor_total: premiosPor11Pontos.reduce((sum: number, p: any) => sum + parseFloat(p.valor_premio), 0)
        },
        premios_maior_pontuador: {
          quantidade: premiosMaiorPontuador.length,
          valor_total: premiosMaiorPontuador.reduce((sum: number, p: any) => sum + parseFloat(p.valor_premio), 0)
        }
      }
    })

  } catch (error) {
    console.error(`❌ Erro ao buscar prêmios do bolão ${params.id}:`, error)
    return NextResponse.json(
      { success: false, error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

// Endpoint para marcar prêmio como pago
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await initializeDatabase()
    
    const bolaoId = parseInt(params.id)
    const body = await request.json()
    const { premio_id, status } = body

    if (!premio_id || !status) {
      return NextResponse.json(
        { success: false, error: "ID do prêmio e status são obrigatórios" },
        { status: 400 }
      )
    }

    if (!['pago', 'pendente', 'cancelado'].includes(status)) {
      return NextResponse.json(
        { success: false, error: "Status inválido. Use: pago, pendente ou cancelado" },
        { status: 400 }
      )
    }

    console.log(`💰 Atualizando status do prêmio ${premio_id} para ${status}`)

    // Verificar se o prêmio pertence ao bolão
    const premio = await executeQuery(`
      SELECT id FROM premios WHERE id = ? AND bolao_id = ?
    `, [premio_id, bolaoId])

    if (premio.length === 0) {
      return NextResponse.json(
        { success: false, error: "Prêmio não encontrado neste bolão" },
        { status: 404 }
      )
    }

    // Atualizar status do prêmio
    const updateQuery = status === 'pago' 
      ? `UPDATE premios SET status = ?, data_pagamento = NOW() WHERE id = ?`
      : `UPDATE premios SET status = ?, data_pagamento = NULL WHERE id = ?`

    await executeQuery(updateQuery, [status, premio_id])

    console.log(`✅ Status do prêmio ${premio_id} atualizado para ${status}`)

    return NextResponse.json({
      success: true,
      message: `Prêmio marcado como ${status} com sucesso`
    })

  } catch (error) {
    console.error(`❌ Erro ao atualizar prêmio:`, error)
    return NextResponse.json(
      { success: false, error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
