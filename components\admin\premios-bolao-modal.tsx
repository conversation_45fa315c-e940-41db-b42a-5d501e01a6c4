"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Trophy, Crown, Medal, DollarSign, Users, Calendar, CheckCircle, Clock, XCircle } from "lucide-react"
import { toast } from "sonner"

interface PremiosBolaoModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  bolaoId: number | null
  bolaoNome?: string
}

interface Premio {
  id: number
  usuario_nome: string
  usuario_email: string
  acertos: number
  valor_premio: number
  tipo_premio: string
  status: 'pendente' | 'pago' | 'cancelado'
  data_premio: string
  data_pagamento?: string
}

interface Estatisticas {
  total_premios: number
  valor_total_distribuido: number
  premios_pagos: number
  premios_pendentes: number
  premios_11_pontos: {
    quantidade: number
    valor_total: number
  }
  premios_maior_pontuador: {
    quantidade: number
    valor_total: number
  }
}

export function PremiosBolaoModal({ open, onOpenChange, bolaoId, bolaoNome }: PremiosBolaoModalProps) {
  const [loading, setLoading] = useState(false)
  const [premios, setPremios] = useState<Premio[]>([])
  const [estatisticas, setEstatisticas] = useState<Estatisticas | null>(null)
  const [bolaoInfo, setBolaoInfo] = useState<any>(null)

  useEffect(() => {
    if (open && bolaoId) {
      carregarPremios()
    }
  }, [open, bolaoId])

  const carregarPremios = async () => {
    if (!bolaoId) return

    setLoading(true)
    try {
      const response = await fetch(`/api/boloes/${bolaoId}/premios`)
      const data = await response.json()

      if (data.success) {
        setPremios(data.premios)
        setEstatisticas(data.estatisticas)
        setBolaoInfo(data.bolao)
      } else {
        toast.error("Erro ao carregar prêmios")
      }
    } catch (error) {
      console.error("Erro ao carregar prêmios:", error)
      toast.error("Erro ao carregar prêmios")
    } finally {
      setLoading(false)
    }
  }

  const atualizarStatusPremio = async (premioId: number, novoStatus: string) => {
    if (!bolaoId) return

    try {
      const response = await fetch(`/api/boloes/${bolaoId}/premios`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          premio_id: premioId,
          status: novoStatus
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success(`Prêmio marcado como ${novoStatus}`)
        carregarPremios() // Recarregar dados
      } else {
        toast.error(data.error || "Erro ao atualizar prêmio")
      }
    } catch (error) {
      console.error("Erro ao atualizar prêmio:", error)
      toast.error("Erro ao atualizar prêmio")
    }
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pago':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'pendente':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'cancelado':
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pago':
        return <Badge variant="default" className="bg-green-100 text-green-800">Pago</Badge>
      case 'pendente':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pendente</Badge>
      case 'cancelado':
        return <Badge variant="destructive">Cancelado</Badge>
      default:
        return <Badge variant="outline">Desconhecido</Badge>
    }
  }

  const getTipoIcon = (tipo: string) => {
    return tipo === '11 pontos' ? <Crown className="h-4 w-4 text-yellow-600" /> : <Medal className="h-4 w-4 text-blue-600" />
  }

  if (loading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Carregando prêmios...</DialogTitle>
          </DialogHeader>
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5 text-yellow-600" />
            Prêmios do Bolão: {bolaoNome || bolaoInfo?.nome}
          </DialogTitle>
          <DialogDescription>
            Visualize e gerencie os prêmios distribuídos neste bolão
          </DialogDescription>
        </DialogHeader>

        {estatisticas && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4 text-center">
                <DollarSign className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600">Total Distribuído</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(estatisticas.valor_total_distribuido)}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600">Total de Prêmios</p>
                <p className="text-2xl font-bold text-blue-600">{estatisticas.total_premios}</p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600">Pagos</p>
                <p className="text-2xl font-bold text-green-600">{estatisticas.premios_pagos}</p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <Clock className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600">Pendentes</p>
                <p className="text-2xl font-bold text-yellow-600">{estatisticas.premios_pendentes}</p>
              </CardContent>
            </Card>
          </div>
        )}

        {bolaoInfo && (
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-6">
            <h3 className="font-semibold text-blue-800 mb-2">Configuração de Prêmios</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Premiação 11 Pontos:</span> {formatCurrency(bolaoInfo.premiacao_11_pontos)}
              </div>
              <div>
                <span className="font-medium">Premiação Maior Pontuador:</span> {formatCurrency(bolaoInfo.premiacao_maior_pontuador)}
              </div>
            </div>
          </div>
        )}

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Lista de Prêmios</h3>
          
          {premios.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <Trophy className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">Nenhum prêmio foi distribuído ainda</p>
                <p className="text-sm text-gray-500 mt-2">
                  Os prêmios serão calculados automaticamente quando o bolão for finalizado
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-3">
              {premios.map((premio) => (
                <Card key={premio.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        {getTipoIcon(premio.tipo_premio)}
                        <div>
                          <h4 className="font-semibold">{premio.usuario_nome}</h4>
                          <p className="text-sm text-gray-600">{premio.usuario_email}</p>
                        </div>
                      </div>

                      <div className="flex items-center gap-4">
                        <div className="text-center">
                          <p className="text-sm text-gray-600">Acertos</p>
                          <p className="font-bold text-lg">{premio.acertos}</p>
                        </div>

                        <div className="text-center">
                          <p className="text-sm text-gray-600">Prêmio</p>
                          <p className="font-bold text-lg text-green-600">
                            {formatCurrency(premio.valor_premio)}
                          </p>
                        </div>

                        <div className="text-center">
                          <p className="text-sm text-gray-600">Tipo</p>
                          <Badge variant="outline">{premio.tipo_premio}</Badge>
                        </div>

                        <div className="text-center">
                          <p className="text-sm text-gray-600">Status</p>
                          {getStatusBadge(premio.status)}
                        </div>

                        <div className="flex gap-2">
                          {premio.status === 'pendente' && (
                            <Button
                              size="sm"
                              onClick={() => atualizarStatusPremio(premio.id, 'pago')}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              Marcar como Pago
                            </Button>
                          )}
                          {premio.status === 'pago' && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => atualizarStatusPremio(premio.id, 'pendente')}
                            >
                              Marcar como Pendente
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="mt-3 pt-3 border-t border-gray-200">
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>Prêmio criado: {formatDate(premio.data_premio)}</span>
                        {premio.data_pagamento && (
                          <span>Pago em: {formatDate(premio.data_pagamento)}</span>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        <div className="flex justify-end pt-4 border-t">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Fechar
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
