-- =====================================================
-- INSERT SIMPLES PARA TABELA TIMES
-- =====================================================
-- Execute cada bloco separadamente no phpMyAdmin

USE `sistema-bolao-top`;

-- =====================================================
-- BLOCO 1: TIMES BRASILEIROS (1-10)
-- =====================================================
INSERT INTO `times` (`id`, `nome`, `nome_curto`, `cidade`, `estado`, `pais`, `logo_url`, `api_id`, `image_id`) VALUES
(1, 'Clube de Regatas do Flamengo', 'Flamengo', 'Rio de Janeiro', 'RJ', 'Brasil', 'https://logodetimes.com/wp-content/uploads/flamengo.png', '1783', '1783'),
(2, 'Sociedade Esportiva Palmeiras', 'Palmeiras', 'São Paulo', 'SP', 'Brasil', 'https://crests.football-data.org/1769.png', '1769', '1769'),
(3, 'São Paulo Futebol Clube', 'São Paulo', 'São Paulo', 'SP', 'Brasil', 'https://crests.football-data.org/1776.png', '1776', '1776'),
(4, 'Sport Club Corinthians Paulista', 'Corinthians', 'São Paulo', 'SP', 'Brasil', 'https://logodetimes.com/wp-content/uploads/corinthians.png', '1779', '1779'),
(5, 'Santos Futebol Clube', 'Santos', 'Santos', 'SP', 'Brasil', 'https://logodetimes.com/wp-content/uploads/santos.png', '1780', '1780'),
(6, 'Grêmio Foot-Ball Porto Alegrense', 'Grêmio', 'Porto Alegre', 'RS', 'Brasil', 'https://crests.football-data.org/1767.png', '1767', '1767'),
(7, 'Sport Club Internacional', 'Internacional', 'Porto Alegre', 'RS', 'Brasil', 'https://logodetimes.com/wp-content/uploads/internacional.png', '1768', '1768'),
(8, 'Clube Atlético Mineiro', 'Atlético-MG', 'Belo Horizonte', 'MG', 'Brasil', 'https://logodetimes.com/wp-content/uploads/atletico-mg.png', '1766', '1766'),
(9, 'Cruzeiro Esporte Clube', 'Cruzeiro', 'Belo Horizonte', 'MG', 'Brasil', 'https://logodetimes.com/wp-content/uploads/cruzeiro.png', '1771', '1771'),
(10, 'Botafogo de Futebol e Regatas', 'Botafogo', 'Rio de Janeiro', 'RJ', 'Brasil', 'https://crests.football-data.org/1770.png', '1770', '1770')
ON DUPLICATE KEY UPDATE
nome = VALUES(nome), nome_curto = VALUES(nome_curto), cidade = VALUES(cidade), 
estado = VALUES(estado), pais = VALUES(pais), logo_url = VALUES(logo_url), 
api_id = VALUES(api_id), image_id = VALUES(image_id);

-- =====================================================
-- BLOCO 2: TIMES BRASILEIROS (11-20)
-- =====================================================
INSERT INTO `times` (`id`, `nome`, `nome_curto`, `cidade`, `estado`, `pais`, `logo_url`, `api_id`, `image_id`) VALUES
(11, 'Club de Regatas Vasco da Gama', 'Vasco', 'Rio de Janeiro', 'RJ', 'Brasil', 'https://logodetimes.com/wp-content/uploads/vasco.png', '1897', '1897'),
(12, 'Fluminense Football Club', 'Fluminense', 'Rio de Janeiro', 'RJ', 'Brasil', 'https://crests.football-data.org/1765.png', '1765', '1765'),
(13, 'Fortaleza Esporte Clube', 'Fortaleza', 'Fortaleza', 'CE', 'Brasil', 'https://crests.football-data.org/3984.png', '3984', '3984'),
(14, 'Red Bull Bragantino', 'Bragantino', 'Bragança Paulista', 'SP', 'Brasil', 'https://crests.football-data.org/4286.png', '4286', '4286'),
(15, 'Athletico Paranaense', 'Athletico-PR', 'Curitiba', 'PR', 'Brasil', 'https://logodetimes.com/wp-content/uploads/athletico-pr.png', '1772', '1772'),
(16, 'Coritiba Foot Ball Club', 'Coritiba', 'Curitiba', 'PR', 'Brasil', 'https://logodetimes.com/wp-content/uploads/coritiba.png', '1773', '1773'),
(17, 'Goiás Esporte Clube', 'Goiás', 'Goiânia', 'GO', 'Brasil', 'https://logodetimes.com/wp-content/uploads/goias.png', '1774', '1774'),
(18, 'Ceará Sporting Club', 'Ceará', 'Fortaleza', 'CE', 'Brasil', 'https://logodetimes.com/wp-content/uploads/ceara.png', '1775', '1775'),
(19, 'Sport Club do Recife', 'Sport', 'Recife', 'PE', 'Brasil', 'https://logodetimes.com/wp-content/uploads/sport.png', '1778', '1778'),
(20, 'Esporte Clube Bahia', 'Bahia', 'Salvador', 'BA', 'Brasil', 'https://crests.football-data.org/1777.png', '1777', '1777')
ON DUPLICATE KEY UPDATE
nome = VALUES(nome), nome_curto = VALUES(nome_curto), cidade = VALUES(cidade), 
estado = VALUES(estado), pais = VALUES(pais), logo_url = VALUES(logo_url), 
api_id = VALUES(api_id), image_id = VALUES(image_id);

-- =====================================================
-- BLOCO 3: PREMIER LEAGUE (21-30)
-- =====================================================
INSERT INTO `times` (`id`, `nome`, `nome_curto`, `cidade`, `estado`, `pais`, `logo_url`, `api_id`, `image_id`) VALUES
(21, 'Manchester City Football Club', 'Man City', 'Manchester', NULL, 'Inglaterra', 'https://crests.football-data.org/65.png', '65', '65'),
(22, 'Arsenal Football Club', 'Arsenal', 'London', NULL, 'Inglaterra', 'https://crests.football-data.org/57.png', '57', '57'),
(23, 'Liverpool Football Club', 'Liverpool', 'Liverpool', NULL, 'Inglaterra', 'https://crests.football-data.org/64.png', '64', '64'),
(24, 'Chelsea Football Club', 'Chelsea', 'London', NULL, 'Inglaterra', 'https://crests.football-data.org/61.png', '61', '61'),
(25, 'Manchester United Football Club', 'Man United', 'Manchester', NULL, 'Inglaterra', 'https://crests.football-data.org/66.png', '66', '66'),
(26, 'Tottenham Hotspur Football Club', 'Tottenham', 'London', NULL, 'Inglaterra', 'https://crests.football-data.org/73.png', '73', '73'),
(27, 'Newcastle United Football Club', 'Newcastle', 'Newcastle', NULL, 'Inglaterra', 'https://crests.football-data.org/67.png', '67', '67'),
(28, 'Brighton & Hove Albion Football Club', 'Brighton', 'Brighton', NULL, 'Inglaterra', 'https://crests.football-data.org/397.png', '397', '397'),
(29, 'Aston Villa Football Club', 'Aston Villa', 'Birmingham', NULL, 'Inglaterra', 'https://crests.football-data.org/58.png', '58', '58'),
(30, 'West Ham United Football Club', 'West Ham', 'London', NULL, 'Inglaterra', 'https://crests.football-data.org/563.png', '563', '563')
ON DUPLICATE KEY UPDATE
nome = VALUES(nome), nome_curto = VALUES(nome_curto), cidade = VALUES(cidade), 
estado = VALUES(estado), pais = VALUES(pais), logo_url = VALUES(logo_url), 
api_id = VALUES(api_id), image_id = VALUES(image_id);

-- =====================================================
-- BLOCO 4: LA LIGA (31-40)
-- =====================================================
INSERT INTO `times` (`id`, `nome`, `nome_curto`, `cidade`, `estado`, `pais`, `logo_url`, `api_id`, `image_id`) VALUES
(31, 'Real Madrid Club de Fútbol', 'Real Madrid', 'Madrid', NULL, 'Espanha', 'https://crests.football-data.org/86.png', '86', '86'),
(32, 'Futbol Club Barcelona', 'Barcelona', 'Barcelona', NULL, 'Espanha', 'https://crests.football-data.org/81.png', '81', '81'),
(33, 'Club Atlético de Madrid', 'Atlético Madrid', 'Madrid', NULL, 'Espanha', 'https://crests.football-data.org/78.png', '78', '78'),
(34, 'Sevilla Fútbol Club', 'Sevilla', 'Sevilla', NULL, 'Espanha', 'https://crests.football-data.org/559.png', '559', '559'),
(35, 'Real Sociedad de Fútbol', 'Real Sociedad', 'San Sebastián', NULL, 'Espanha', 'https://crests.football-data.org/92.png', '92', '92'),
(36, 'Real Betis Balompié', 'Real Betis', 'Sevilla', NULL, 'Espanha', 'https://crests.football-data.org/90.png', '90', '90'),
(37, 'Valencia Club de Fútbol', 'Valencia', 'Valencia', NULL, 'Espanha', 'https://crests.football-data.org/95.png', '95', '95'),
(38, 'Villarreal Club de Fútbol', 'Villarreal', 'Villarreal', NULL, 'Espanha', 'https://crests.football-data.org/94.png', '94', '94'),
(39, 'Athletic Club', 'Athletic Bilbao', 'Bilbao', NULL, 'Espanha', 'https://crests.football-data.org/77.png', '77', '77'),
(40, 'Getafe Club de Fútbol', 'Getafe', 'Getafe', NULL, 'Espanha', 'https://crests.football-data.org/82.png', '82', '82')
ON DUPLICATE KEY UPDATE
nome = VALUES(nome), nome_curto = VALUES(nome_curto), cidade = VALUES(cidade), 
estado = VALUES(estado), pais = VALUES(pais), logo_url = VALUES(logo_url), 
api_id = VALUES(api_id), image_id = VALUES(image_id);

-- =====================================================
-- BLOCO 5: BUNDESLIGA (41-50)
-- =====================================================
INSERT INTO `times` (`id`, `nome`, `nome_curto`, `cidade`, `estado`, `pais`, `logo_url`, `api_id`, `image_id`) VALUES
(41, 'FC Bayern München', 'Bayern Munich', 'München', NULL, 'Alemanha', 'https://crests.football-data.org/5.png', '5', '5'),
(42, 'Borussia Dortmund', 'Dortmund', 'Dortmund', NULL, 'Alemanha', 'https://crests.football-data.org/4.png', '4', '4'),
(43, 'RB Leipzig', 'Leipzig', 'Leipzig', NULL, 'Alemanha', 'https://crests.football-data.org/721.png', '721', '721'),
(44, 'Bayer 04 Leverkusen', 'Leverkusen', 'Leverkusen', NULL, 'Alemanha', 'https://crests.football-data.org/3.png', '3', '3'),
(45, 'Borussia Mönchengladbach', 'Gladbach', 'Mönchengladbach', NULL, 'Alemanha', 'https://crests.football-data.org/18.png', '18', '18'),
(46, 'Eintracht Frankfurt', 'Frankfurt', 'Frankfurt', NULL, 'Alemanha', 'https://crests.football-data.org/19.png', '19', '19'),
(47, 'VfL Wolfsburg', 'Wolfsburg', 'Wolfsburg', NULL, 'Alemanha', 'https://crests.football-data.org/11.png', '11', '11'),
(48, 'FC Schalke 04', 'Schalke', 'Gelsenkirchen', NULL, 'Alemanha', 'https://crests.football-data.org/6.png', '6', '6'),
(49, 'TSG 1899 Hoffenheim', 'Hoffenheim', 'Hoffenheim', NULL, 'Alemanha', 'https://crests.football-data.org/2.png', '2', '2'),
(50, 'Hertha BSC', 'Hertha Berlin', 'Berlin', NULL, 'Alemanha', 'https://crests.football-data.org/9.png', '9', '9')
ON DUPLICATE KEY UPDATE
nome = VALUES(nome), nome_curto = VALUES(nome_curto), cidade = VALUES(cidade), 
estado = VALUES(estado), pais = VALUES(pais), logo_url = VALUES(logo_url), 
api_id = VALUES(api_id), image_id = VALUES(image_id);

-- =====================================================
-- VERIFICAR RESULTADOS
-- =====================================================
SELECT 'Times inseridos com sucesso!' as resultado;

SELECT 
  pais,
  COUNT(*) as total_times
FROM times 
WHERE id BETWEEN 1 AND 50
GROUP BY pais
ORDER BY total_times DESC;
