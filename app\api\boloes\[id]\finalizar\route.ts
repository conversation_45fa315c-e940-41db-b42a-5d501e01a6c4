import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery, executeQuerySingle } from "@/lib/database-config"

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await initializeDatabase()
    
    const bolaoId = parseInt(params.id)
    
    console.log(`🏁 Finalizando bolão ${bolaoId}`)

    // Verificar se o bolão existe e está ativo
    const bolao = await executeQuerySingle(`
      SELECT id, nome, status, premiacao_11_pontos, premiacao_maior_pontuador
      FROM boloes 
      WHERE id = ?
    `, [bolaoId])

    if (!bolao) {
      return NextResponse.json(
        { success: false, error: "Bolão não encontrado" },
        { status: 404 }
      )
    }

    if (bolao.status === 'encerrado') {
      return NextResponse.json(
        { success: false, error: "Bolão já foi finalizado" },
        { status: 400 }
      )
    }

    // Buscar ranking final dos apostadores
    const ranking = await executeQuery(`
      SELECT 
        u.id,
        u.nome,
        COUNT(CASE WHEN ad.acertou = 1 THEN 1 END) as acertos,
        COUNT(ad.id) as total_apostas,
        ROUND((COUNT(CASE WHEN ad.acertou = 1 THEN 1 END) / COUNT(ad.id)) * 100, 2) as percentual_acertos,
        a.id as aposta_id,
        a.data_aposta
      FROM usuarios u
      JOIN apostas a ON u.id = a.usuario_id
      JOIN aposta_detalhes ad ON a.id = ad.aposta_id
      JOIN jogos j ON ad.jogo_id = j.id
      WHERE a.bolao_id = ? AND a.status = 'paga'
      GROUP BY u.id, u.nome, a.id, a.data_aposta
      ORDER BY acertos DESC, percentual_acertos DESC, a.data_aposta ASC
    `, [bolaoId])

    if (ranking.length === 0) {
      return NextResponse.json(
        { success: false, error: "Nenhuma aposta encontrada para este bolão" },
        { status: 400 }
      )
    }

    // Calcular e distribuir prêmios
    const resultadoPremios = await calcularEDistribuirPremios(bolaoId, ranking, bolao)

    // Atualizar status do bolão para encerrado
    await executeQuery(`
      UPDATE boloes SET status = 'encerrado' WHERE id = ?
    `, [bolaoId])

    console.log(`✅ Bolão ${bolaoId} finalizado com sucesso`)

    return NextResponse.json({
      success: true,
      message: "Bolão finalizado com sucesso",
      bolao: {
        id: bolaoId,
        nome: bolao.nome,
        status: 'encerrado'
      },
      ranking: ranking.slice(0, 10), // Top 10
      premios: resultadoPremios
    })

  } catch (error) {
    console.error(`❌ Erro ao finalizar bolão ${params.id}:`, error)
    return NextResponse.json(
      { success: false, error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

// Função para calcular e distribuir prêmios
async function calcularEDistribuirPremios(bolaoId: number, ranking: any[], bolao: any) {
  try {
    console.log(`🏆 Calculando prêmios para bolão ${bolaoId}`)

    const premiacao11Pontos = parseFloat(bolao.premiacao_11_pontos) || 500.00
    const premiacaoMaiorPontuador = parseFloat(bolao.premiacao_maior_pontuador) || 10.00

    // Verificar se já existem prêmios calculados
    const premiosExistentes = await executeQuery(`
      SELECT COUNT(*) as total FROM premios WHERE bolao_id = ?
    `, [bolaoId])

    if (premiosExistentes[0]?.total > 0) {
      console.log(`ℹ️ Prêmios já calculados para bolão ${bolaoId}`)
      return { message: "Prêmios já foram calculados anteriormente" }
    }

    const premiosDistribuidos = []

    // Encontrar quem fez 11 pontos
    const vencedores11Pontos = ranking.filter((r: any) => r.acertos === 11)
    
    if (vencedores11Pontos.length > 0) {
      // Tem vencedor(es) de 11 pontos - não há maior pontuador
      console.log(`🎯 ${vencedores11Pontos.length} vencedor(es) com 11 pontos encontrado(s)`)
      
      const valorPorVencedor = premiacao11Pontos / vencedores11Pontos.length
      
      for (const vencedor of vencedores11Pontos) {
        await executeQuery(`
          INSERT INTO premios (bolao_id, usuario_id, aposta_id, acertos, valor_premio, status, data_premio)
          VALUES (?, ?, ?, ?, ?, 'pendente', NOW())
        `, [bolaoId, vencedor.id, vencedor.aposta_id, vencedor.acertos, valorPorVencedor])
        
        premiosDistribuidos.push({
          usuario: vencedor.nome,
          acertos: vencedor.acertos,
          valor: valorPorVencedor,
          tipo: '11 pontos'
        })
        
        console.log(`💰 Prêmio de R$ ${valorPorVencedor.toFixed(2)} para ${vencedor.nome} (11 pontos)`)
      }
    } else {
      // Não há vencedor de 11 pontos - buscar maior pontuador
      const maiorPontuacao = Math.max(...ranking.map((r: any) => r.acertos))
      
      if (maiorPontuacao > 0) {
        const maioresPontuadores = ranking.filter((r: any) => r.acertos === maiorPontuacao)
        
        console.log(`🥇 ${maioresPontuadores.length} maior(es) pontuador(es) com ${maiorPontuacao} pontos`)
        
        const valorPorMaiorPontuador = premiacaoMaiorPontuador / maioresPontuadores.length
        
        for (const maiorPontuador of maioresPontuadores) {
          await executeQuery(`
            INSERT INTO premios (bolao_id, usuario_id, aposta_id, acertos, valor_premio, status, data_premio)
            VALUES (?, ?, ?, ?, ?, 'pendente', NOW())
          `, [bolaoId, maiorPontuador.id, maiorPontuador.aposta_id, maiorPontuador.acertos, valorPorMaiorPontuador])
          
          premiosDistribuidos.push({
            usuario: maiorPontuador.nome,
            acertos: maiorPontuador.acertos,
            valor: valorPorMaiorPontuador,
            tipo: 'maior pontuador'
          })
          
          console.log(`🏅 Prêmio de R$ ${valorPorMaiorPontuador.toFixed(2)} para ${maiorPontuador.nome} (${maiorPontuacao} pontos - maior pontuador)`)
        }
      }
    }

    console.log(`✅ ${premiosDistribuidos.length} prêmios distribuídos para bolão ${bolaoId}`)
    
    return {
      total_premios: premiosDistribuidos.length,
      premios: premiosDistribuidos,
      valor_total_distribuido: premiosDistribuidos.reduce((sum, p) => sum + p.valor, 0)
    }

  } catch (error) {
    console.error(`❌ Erro ao calcular prêmios do bolão ${bolaoId}:`, error)
    throw error
  }
}
