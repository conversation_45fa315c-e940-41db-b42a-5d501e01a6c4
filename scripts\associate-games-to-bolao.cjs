const mysql = require('mysql2/promise')

async function associateGamesToBolao() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '',
    database: 'sistema-bolao-top'
  })

  try {
    console.log('✅ Conectado ao banco de dados')

    // Buscar jogos futuros para associar ao bolão
    const [jogos] = await connection.execute(`
      SELECT j.*, 
             tc.nome as time_casa_nome,
             tf.nome as time_fora_nome,
             c.nome as campeonato_nome
      FROM jogos j
      LEFT JOIN times tc ON j.time_casa_id = tc.id
      LEFT JOIN times tf ON j.time_fora_id = tf.id
      LEFT JOIN campeonatos c ON j.campeonato_id = c.id
      WHERE j.status IN ('agendado', 'ao_vivo')
      AND j.data_jogo >= NOW()
      ORDER BY j.data_jogo ASC
      LIMIT 11
    `)

    console.log(`📊 Encontrados ${jogos.length} jogos para associar`)

    if (jogos.length === 0) {
      console.log('❌ Nenhum jogo futuro encontrado')
      return
    }

    // Mostrar os jogos que serão associados
    console.log('🎯 Jogos que serão associados ao bolão 105:')
    jogos.forEach((jogo, index) => {
      console.log(`   ${index + 1}. ${jogo.time_casa_nome || 'Time Casa'} vs ${jogo.time_fora_nome || 'Time Fora'} - ${jogo.campeonato_nome || 'Campeonato'} (${jogo.data_jogo})`)
    })

    // Associar jogos ao bolão 105
    for (let i = 0; i < jogos.length; i++) {
      const jogo = jogos[i]
      await connection.execute(`
        INSERT INTO bolao_jogos (bolao_id, jogo_id)
        VALUES (105, ?)
        ON DUPLICATE KEY UPDATE jogo_id = jogo_id
      `, [jogo.id])
    }

    console.log(`✅ ${jogos.length} jogos associados ao bolão 105`)

    // Atualizar o campo partidas_selecionadas do bolão
    const partidasSelecionadas = jogos.map(jogo => ({
      id: jogo.id,
      time_casa: jogo.time_casa_nome || 'Time Casa',
      time_fora: jogo.time_fora_nome || 'Time Fora',
      campeonato: jogo.campeonato_nome || 'Campeonato',
      data_jogo: jogo.data_jogo
    }))

    await connection.execute(`
      UPDATE boloes 
      SET partidas_selecionadas = ?
      WHERE id = 105
    `, [JSON.stringify(partidasSelecionadas)])

    console.log('✅ Campo partidas_selecionadas atualizado')

    // Verificar se funcionou
    const [verificacao] = await connection.execute(`
      SELECT COUNT(*) as total 
      FROM bolao_jogos 
      WHERE bolao_id = 105
    `)

    console.log(`🎉 Verificação: ${verificacao[0].total} jogos associados ao bolão 105`)

  } catch (error) {
    console.error('❌ Erro:', error)
  } finally {
    await connection.end()
  }
}

associateGamesToBolao()
